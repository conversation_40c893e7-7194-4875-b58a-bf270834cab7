from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from core.models import BaseModel, AcademicYear, Semester
from students.models import Grade, Class, Student


class Subject(BaseModel):
    """
    Subject model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Subject Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Subject Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Subject Code')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    grades = models.ManyToManyField(
        Grade,
        related_name='subjects',
        verbose_name=_('Grades')
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name=_('Is Mandatory')
    )

    credit_hours = models.PositiveIntegerField(
        default=1,
        verbose_name=_('Credit Hours')
    )

    class Meta:
        verbose_name = _('Subject')
        verbose_name_plural = _('Subjects')
        ordering = ['name']

    def __str__(self):
        return self.name


class Teacher(BaseModel):
    """
    Teacher model
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='teacher_profile',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('User Account')
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Employee ID')
    )

    subjects = models.ManyToManyField(
        Subject,
        related_name='teachers',
        verbose_name=_('Subjects')
    )

    hire_date = models.DateField(
        verbose_name=_('Hire Date')
    )

    qualification = models.CharField(
        max_length=200,
        verbose_name=_('Qualification')
    )

    experience_years = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Experience Years')
    )

    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Salary')
    )

    department = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Department')
    )

    class Meta:
        verbose_name = _('Teacher')
        verbose_name_plural = _('Teachers')
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} ({self.employee_id})"


class ClassSubject(BaseModel):
    """
    Class-Subject assignment model
    """
    class_obj = models.ForeignKey(
        Class,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Class')
    )

    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Subject')
    )

    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Teacher')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Academic Year')
    )

    weekly_hours = models.PositiveIntegerField(
        default=1,
        verbose_name=_('Weekly Hours')
    )

    class Meta:
        verbose_name = _('Class Subject')
        verbose_name_plural = _('Class Subjects')
        unique_together = ['class_obj', 'subject', 'academic_year']
        ordering = ['class_obj', 'subject']

    def __str__(self):
        return f"{self.class_obj} - {self.subject} ({self.teacher})"


class Schedule(BaseModel):
    """
    Class schedule model
    """
    DAYS_OF_WEEK = (
        ('monday', _('Monday')),
        ('tuesday', _('Tuesday')),
        ('wednesday', _('Wednesday')),
        ('thursday', _('Thursday')),
        ('friday', _('Friday')),
        ('saturday', _('Saturday')),
        ('sunday', _('Sunday')),
    )

    class_subject = models.ForeignKey(
        ClassSubject,
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name=_('Class Subject')
    )

    day_of_week = models.CharField(
        max_length=10,
        choices=DAYS_OF_WEEK,
        verbose_name=_('Day of Week')
    )

    start_time = models.TimeField(
        verbose_name=_('Start Time')
    )

    end_time = models.TimeField(
        verbose_name=_('End Time')
    )

    room_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Room Number')
    )

    class Meta:
        verbose_name = _('Schedule')
        verbose_name_plural = _('Schedules')
        ordering = ['day_of_week', 'start_time']

    def __str__(self):
        return f"{self.class_subject} - {self.get_day_of_week_display()} {self.start_time}"
