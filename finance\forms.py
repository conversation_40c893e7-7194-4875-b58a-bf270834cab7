from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML
from crispy_forms.bootstrap import FormActions
from .models import (
    Account, AccountType, CostCenter, JournalEntry, FinancialYear,
    Bank, Invoice, InvoiceItem, Payment, PaymentItem, StudentFee, FeeType, GradeFee
)
from students.models import Student


class AccountForm(forms.ModelForm):
    """
    Account creation and update form
    """
    class Meta:
        model = Account
        fields = [
            'account_type', 'parent', 'code', 'name', 'name_ar',
            'description', 'opening_balance', 'is_control_account'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'opening_balance': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Account Information'),
                Row(
                    Column('account_type', css_class='form-group col-md-6 mb-3'),
                    Column('parent', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('code', css_class='form-group col-md-6 mb-3'),
                    Column('name', css_class='form-group col-md-6 mb-3'),
                ),
                'name_ar',
                'description',
                Row(
                    Column('opening_balance', css_class='form-group col-md-6 mb-3'),
                    Column('is_control_account', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            FormActions(
                Submit('submit', _('Save Account'), css_class='btn btn-primary'),
                HTML('<a href="{% url "finance:accounts_tree" %}" class="btn btn-secondary ms-2">' + str(_('Cancel')) + '</a>'),
            )
        )


class JournalEntryForm(forms.ModelForm):
    """
    Journal entry form
    """
    class Meta:
        model = JournalEntry
        fields = [
            'reference_number', 'entry_date', 'entry_type', 'description',
            'account', 'cost_center', 'debit_amount', 'credit_amount'
        ]
        widgets = {
            'entry_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'debit_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'credit_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        debit_amount = cleaned_data.get('debit_amount', 0)
        credit_amount = cleaned_data.get('credit_amount', 0)

        if debit_amount > 0 and credit_amount > 0:
            raise ValidationError(_('An entry cannot have both debit and credit amounts'))
        if debit_amount == 0 and credit_amount == 0:
            raise ValidationError(_('An entry must have either a debit or credit amount'))

        return cleaned_data


class PaymentForm(forms.ModelForm):
    """
    Payment form
    """
    class Meta:
        model = Payment
        fields = [
            'student', 'receipt_number', 'amount', 'payment_date',
            'payment_method', 'reference_number', 'notes'
        ]
        widgets = {
            'payment_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Payment Information'),
                Row(
                    Column('student', css_class='form-group col-md-6 mb-3'),
                    Column('receipt_number', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('amount', css_class='form-group col-md-6 mb-3'),
                    Column('payment_date', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('payment_method', css_class='form-group col-md-6 mb-3'),
                    Column('reference_number', css_class='form-group col-md-6 mb-3'),
                ),
                'notes',
            ),
            FormActions(
                Submit('submit', _('Record Payment'), css_class='btn btn-primary'),
            )
        )


class InvoiceForm(forms.ModelForm):
    """
    Invoice form
    """
    class Meta:
        model = Invoice
        fields = [
            'student', 'invoice_type', 'invoice_date', 'due_date',
            'subtotal', 'tax_amount', 'discount_amount', 'notes'
        ]
        widgets = {
            'invoice_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'due_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'subtotal': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'tax_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'discount_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class CostCenterForm(forms.ModelForm):
    """
    Cost center form
    """
    class Meta:
        model = CostCenter
        fields = ['code', 'name', 'name_ar', 'description', 'manager', 'budget_amount']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'budget_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class BankForm(forms.ModelForm):
    """
    Bank account form
    """
    class Meta:
        model = Bank
        fields = [
            'name', 'name_ar', 'account_number', 'account_name',
            'branch', 'swift_code', 'iban', 'opening_balance', 'account'
        ]
        widgets = {
            'opening_balance': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class FeeTypeForm(forms.ModelForm):
    """
    Fee type form
    """
    class Meta:
        model = FeeType
        fields = ['name', 'name_ar', 'code', 'description', 'amount', 'is_mandatory']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class GradeFeeForm(forms.ModelForm):
    """
    Grade fee form
    """
    class Meta:
        model = GradeFee
        fields = ['grade', 'fee_type', 'amount', 'academic_year']
        widgets = {
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class StudentFeeForm(forms.ModelForm):
    """
    Student fee form
    """
    class Meta:
        model = StudentFee
        fields = ['student', 'grade_fee', 'amount', 'due_date', 'discount_amount', 'notes']
        widgets = {
            'due_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'discount_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class FinancialYearForm(forms.ModelForm):
    """
    Financial year form
    """
    class Meta:
        model = FinancialYear
        fields = ['name', 'start_date', 'end_date', 'is_current']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError(_('Start date must be before end date.'))

        return cleaned_data


# Quick payment form for fees payment page
class QuickPaymentForm(forms.Form):
    """
    Quick payment form for processing multiple fee payments
    """
    student = forms.ModelChoiceField(
        queryset=Student.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Student')
    )
    
    payment_method = forms.ChoiceField(
        choices=Payment.PAYMENT_METHODS,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Payment Method')
    )
    
    amount_paid = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        label=_('Amount Paid')
    )
    
    reference_number = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        label=_('Reference Number')
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        label=_('Notes')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
