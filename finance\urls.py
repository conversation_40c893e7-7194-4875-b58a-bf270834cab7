from django.urls import path
from . import views

app_name = 'finance'

urlpatterns = [
    # Finance Dashboard
    path('', views.FinanceDashboardView.as_view(), name='dashboard'),
    path('accounts-tree/', views.AccountsTreeView.as_view(), name='accounts_tree'),
    path('journal-entries/', views.JournalEntriesView.as_view(), name='journal_entries'),

    # Accounts Management
    path('accounts-tree/', views.AccountsTreeView.as_view(), name='accounts_tree'),
    path('cost-center/', views.CostCenterView.as_view(), name='cost_center'),
    path('cost-center/report/', views.CostCenterReportView.as_view(), name='cost_center_report'),
    path('daily-entries/', views.DailyEntriesView.as_view(), name='daily_entries'),
    path('daily-entries/create/', views.CreateDailyEntryView.as_view(), name='create_daily_entry'),
    path('daily-entries/history/', views.DailyEntriesHistoryView.as_view(), name='daily_entries_history'),
    path('financial-years/', views.FinancialYearsView.as_view(), name='financial_years'),
    path('banks/', views.BanksView.as_view(), name='banks'),
    path('safes/', views.SafesView.as_view(), name='safes'),
    path('receipt-voucher/', views.ReceiptVoucherView.as_view(), name='receipt_voucher'),
    path('exchange-permission/', views.ExchangePermissionView.as_view(), name='exchange_permission'),
    path('trial-balance/', views.TrialBalanceView.as_view(), name='trial_balance'),
    path('account-statement/', views.AccountStatementView.as_view(), name='account_statement'),
    path('search-receipts/', views.SearchReceiptsView.as_view(), name='search_receipts'),
    path('monthly-statement/', views.MonthlyAccountStatementView.as_view(), name='monthly_statement'),
    path('opening-balances/', views.OpeningBalancesView.as_view(), name='opening_balances'),
    path('cash-bank-statement/', views.CashBankStatementView.as_view(), name='cash_bank_statement'),
    path('balance-sheet/', views.BalanceSheetView.as_view(), name='balance_sheet'),
    path('income-statement/', views.IncomeStatementView.as_view(), name='income_statement'),

    # Student Accounts
    path('fees-items/', views.FeesItemsView.as_view(), name='fees_items'),
    path('grouped-fees/', views.GroupedOptionalFeesView.as_view(), name='grouped_fees'),
    path('grade-fees/', views.GradeFeesView.as_view(), name='grade_fees'),
    path('fees-payment/', views.FeesPaymentView.as_view(), name='fees_payment'),
    path('payment-history/', views.PaymentHistoryView.as_view(), name='payment_history'),
    path('tax-code/', views.TaxCodeView.as_view(), name='tax_code'),
    path('daily-payments-report/', views.DailyPaymentsReportView.as_view(), name='daily_payments_report'),
    path('student-debits/', views.StudentDebitsView.as_view(), name='student_debits'),
    path('accounts-aggregate-report/', views.AccountsAggregateReportView.as_view(), name='accounts_aggregate_report'),
    path('payments-by-stages/', views.PaymentsByStagesView.as_view(), name='payments_by_stages'),
    path('payment-account-report/', views.PaymentAccountReportView.as_view(), name='payment_account_report'),
    path('items-saves-report/', views.ItemsSavesReportView.as_view(), name='items_saves_report'),
    path('tax-payment-report/', views.TaxPaymentReportView.as_view(), name='tax_payment_report'),

    # Payment Permissions
    path('payment-permission/', views.PaymentPermissionView.as_view(), name='payment_permission'),
    path('pay-permission/', views.PayPaymentPermissionView.as_view(), name='pay_permission'),
    path('permission-reports/', views.PermissionReportsView.as_view(), name='permission_reports'),

    # Registration and Discounts
    path('registration-fees/', views.RegistrationFeesView.as_view(), name='registration_fees'),
    path('discount-settings/', views.DiscountSettingsView.as_view(), name='discount_settings'),
    path('discount-details/', views.DiscountSettingsDetailsView.as_view(), name='discount_details'),
    path('add-discount/', views.AddDiscountView.as_view(), name='add_discount'),
    path('remove-debits/', views.RemoveStudentDebitsView.as_view(), name='remove_debits'),
    path('discount-report/', views.DiscountReportView.as_view(), name='discount_report'),
    path('bus-payments-report/', views.BusPaymentsReportView.as_view(), name='bus_payments_report'),
    path('fees-requests/', views.FeesRequestsView.as_view(), name='fees_requests'),
    path('recovery-report/', views.RecoveryReportView.as_view(), name='recovery_report'),
    path('payment-statement/', views.PaymentStatementView.as_view(), name='payment_statement'),
    path('total-payment-statement/', views.TotalPaymentStatementView.as_view(), name='total_payment_statement'),
    path('unpaid-fees-report/', views.UnpaidFeesReportView.as_view(), name='unpaid_fees_report'),
    path('opening-balance-student/', views.AddStudentOpeningBalanceView.as_view(), name='opening_balance_student'),
    path('payment-request/', views.PaymentRequestView.as_view(), name='payment_request'),
    path('grouped-discount/', views.GroupedDiscountView.as_view(), name='grouped_discount'),
    path('grouped-payments/', views.GroupedPaymentTransactionsView.as_view(), name='grouped_payments'),

    # Reports
    path('reports/', views.FinancialReportsView.as_view(), name='reports'),
    path('einvoice-report/', views.EInvoiceReportView.as_view(), name='einvoice_report'),
    path('zakat-income-report/', views.ZakatIncomeReportView.as_view(), name='zakat_income_report'),
]
