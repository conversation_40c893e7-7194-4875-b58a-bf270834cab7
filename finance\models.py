from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator
from core.models import BaseModel, AcademicYear
from students.models import Student, Grade


class AccountType(BaseModel):
    """
    Account type model for chart of accounts
    """
    ACCOUNT_TYPES = (
        ('asset', _('Asset')),
        ('liability', _('Liability')),
        ('equity', _('Equity')),
        ('revenue', _('Revenue')),
        ('expense', _('Expense')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('Account Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Account Type Name (Arabic)')
    )

    type = models.CharField(
        max_length=20,
        choices=ACCOUNT_TYPES,
        verbose_name=_('Type')
    )

    class Meta:
        verbose_name = _('Account Type')
        verbose_name_plural = _('Account Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class Account(BaseModel):
    """
    Chart of accounts model
    """
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Account Code')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Account Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Account Name (Arabic)')
    )

    account_type = models.ForeignKey(
        AccountType,
        on_delete=models.CASCADE,
        related_name='accounts',
        verbose_name=_('Account Type')
    )

    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name=_('Parent Account')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    is_header = models.BooleanField(
        default=False,
        verbose_name=_('Is Header Account')
    )

    class Meta:
        verbose_name = _('Account')
        verbose_name_plural = _('Accounts')
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class FeeType(BaseModel):
    """
    Fee type model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Fee Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Fee Type Name (Arabic)')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='fee_types',
        verbose_name=_('Account')
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name=_('Is Mandatory')
    )

    class Meta:
        verbose_name = _('Fee Type')
        verbose_name_plural = _('Fee Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class GradeFee(BaseModel):
    """
    Grade-specific fee structure
    """
    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='fees',
        verbose_name=_('Grade')
    )

    fee_type = models.ForeignKey(
        FeeType,
        on_delete=models.CASCADE,
        related_name='grade_fees',
        verbose_name=_('Fee Type')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='grade_fees',
        verbose_name=_('Academic Year')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    due_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Due Date')
    )

    class Meta:
        verbose_name = _('Grade Fee')
        verbose_name_plural = _('Grade Fees')
        unique_together = ['grade', 'fee_type', 'academic_year']
        ordering = ['grade', 'fee_type']

    def __str__(self):
        return f"{self.grade} - {self.fee_type} ({self.amount})"


class StudentFee(BaseModel):
    """
    Student fee assignment model
    """
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='fees',
        verbose_name=_('Student')
    )

    grade_fee = models.ForeignKey(
        GradeFee,
        on_delete=models.CASCADE,
        related_name='student_fees',
        verbose_name=_('Grade Fee')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name=_('Discount Amount')
    )

    due_date = models.DateField(
        verbose_name=_('Due Date')
    )

    is_paid = models.BooleanField(
        default=False,
        verbose_name=_('Is Paid')
    )

    paid_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Paid Date')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Student Fee')
        verbose_name_plural = _('Student Fees')
        unique_together = ['student', 'grade_fee']
        ordering = ['student', 'due_date']

    def __str__(self):
        return f"{self.student} - {self.grade_fee.fee_type} ({self.amount})"

    @property
    def net_amount(self):
        return self.amount - self.discount_amount


class Payment(BaseModel):
    """
    Payment model
    """
    PAYMENT_METHODS = (
        ('cash', _('Cash')),
        ('bank_transfer', _('Bank Transfer')),
        ('check', _('Check')),
        ('credit_card', _('Credit Card')),
        ('online', _('Online Payment')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('Student')
    )

    receipt_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Receipt Number')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    payment_date = models.DateField(
        verbose_name=_('Payment Date')
    )

    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHODS,
        verbose_name=_('Payment Method')
    )

    reference_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Reference Number')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    received_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_payments',
        verbose_name=_('Received By')
    )

    class Meta:
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.receipt_number} - {self.student} ({self.amount})"


class PaymentItem(BaseModel):
    """
    Payment item model - links payments to specific fees
    """
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Payment')
    )

    student_fee = models.ForeignKey(
        StudentFee,
        on_delete=models.CASCADE,
        related_name='payment_items',
        verbose_name=_('Student Fee')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    class Meta:
        verbose_name = _('Payment Item')
        verbose_name_plural = _('Payment Items')
        ordering = ['payment', 'student_fee']

    def __str__(self):
        return f"{self.payment.receipt_number} - {self.student_fee.grade_fee.fee_type}"
