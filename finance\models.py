from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator
from core.models import BaseModel, AcademicYear
from students.models import Student, Grade


class AccountType(BaseModel):
    """
    Account type model for chart of accounts
    """
    ACCOUNT_TYPES = (
        ('asset', _('Asset')),
        ('liability', _('Liability')),
        ('equity', _('Equity')),
        ('revenue', _('Revenue')),
        ('expense', _('Expense')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('Account Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Account Type Name (Arabic)')
    )

    type = models.CharField(
        max_length=20,
        choices=ACCOUNT_TYPES,
        verbose_name=_('Type')
    )

    class Meta:
        verbose_name = _('Account Type')
        verbose_name_plural = _('Account Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class Account(BaseModel):
    """
    Chart of accounts model
    """
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Account Code')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Account Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Account Name (Arabic)')
    )

    account_type = models.ForeignKey(
        AccountType,
        on_delete=models.CASCADE,
        related_name='accounts',
        verbose_name=_('Account Type')
    )

    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name=_('Parent Account')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    is_header = models.BooleanField(
        default=False,
        verbose_name=_('Is Header Account')
    )

    class Meta:
        verbose_name = _('Account')
        verbose_name_plural = _('Accounts')
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class FeeType(BaseModel):
    """
    Fee type model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Fee Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Fee Type Name (Arabic)')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='fee_types',
        verbose_name=_('Account')
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name=_('Is Mandatory')
    )

    class Meta:
        verbose_name = _('Fee Type')
        verbose_name_plural = _('Fee Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class GradeFee(BaseModel):
    """
    Grade-specific fee structure
    """
    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='fees',
        verbose_name=_('Grade')
    )

    fee_type = models.ForeignKey(
        FeeType,
        on_delete=models.CASCADE,
        related_name='grade_fees',
        verbose_name=_('Fee Type')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='grade_fees',
        verbose_name=_('Academic Year')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    due_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Due Date')
    )

    class Meta:
        verbose_name = _('Grade Fee')
        verbose_name_plural = _('Grade Fees')
        unique_together = ['grade', 'fee_type', 'academic_year']
        ordering = ['grade', 'fee_type']

    def __str__(self):
        return f"{self.grade} - {self.fee_type} ({self.amount})"


class StudentFee(BaseModel):
    """
    Student fee assignment model
    """
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='fees',
        verbose_name=_('Student')
    )

    grade_fee = models.ForeignKey(
        GradeFee,
        on_delete=models.CASCADE,
        related_name='student_fees',
        verbose_name=_('Grade Fee')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name=_('Discount Amount')
    )

    due_date = models.DateField(
        verbose_name=_('Due Date')
    )

    is_paid = models.BooleanField(
        default=False,
        verbose_name=_('Is Paid')
    )

    paid_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Paid Date')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Student Fee')
        verbose_name_plural = _('Student Fees')
        unique_together = ['student', 'grade_fee']
        ordering = ['student', 'due_date']

    def __str__(self):
        return f"{self.student} - {self.grade_fee.fee_type} ({self.amount})"

    @property
    def net_amount(self):
        return self.amount - self.discount_amount


class Payment(BaseModel):
    """
    Payment model
    """
    PAYMENT_METHODS = (
        ('cash', _('Cash')),
        ('bank_transfer', _('Bank Transfer')),
        ('check', _('Check')),
        ('credit_card', _('Credit Card')),
        ('online', _('Online Payment')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('Student')
    )

    receipt_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Receipt Number')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    payment_date = models.DateField(
        verbose_name=_('Payment Date')
    )

    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHODS,
        verbose_name=_('Payment Method')
    )

    reference_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Reference Number')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    received_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_payments',
        verbose_name=_('Received By')
    )

    class Meta:
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.receipt_number} - {self.student} ({self.amount})"


class PaymentItem(BaseModel):
    """
    Payment item model - links payments to specific fees
    """
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Payment')
    )

    student_fee = models.ForeignKey(
        StudentFee,
        on_delete=models.CASCADE,
        related_name='payment_items',
        verbose_name=_('Student Fee')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    class Meta:
        verbose_name = _('Payment Item')
        verbose_name_plural = _('Payment Items')
        ordering = ['payment', 'student_fee']

    def __str__(self):
        return f"{self.payment.receipt_number} - {self.student_fee.grade_fee.fee_type}"





class Account(BaseModel):
    """
    Chart of accounts model
    """
    account_type = models.ForeignKey(
        AccountType,
        on_delete=models.CASCADE,
        related_name='accounts',
        verbose_name=_('Account Type')
    )

    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name=_('Parent Account')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Account Code')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Account Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Account Name (Arabic)')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Opening Balance')
    )

    is_control_account = models.BooleanField(
        default=False,
        verbose_name=_('Is Control Account')
    )

    class Meta:
        verbose_name = _('Account')
        verbose_name_plural = _('Accounts')
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def current_balance(self):
        """Calculate current balance based on journal entries"""
        from django.db.models import Sum, Q

        debits = JournalEntry.objects.filter(
            account=self,
            is_posted=True
        ).aggregate(total=Sum('debit_amount'))['total'] or 0

        credits = JournalEntry.objects.filter(
            account=self,
            is_posted=True
        ).aggregate(total=Sum('credit_amount'))['total'] or 0

        if self.account_type.category in ['asset', 'expense']:
            return self.opening_balance + debits - credits
        else:  # liability, equity, revenue
            return self.opening_balance + credits - debits

    @property
    def level(self):
        """Get the level of this account in the hierarchy"""
        level = 0
        parent = self.parent
        while parent:
            level += 1
            parent = parent.parent
        return level


class CostCenter(BaseModel):
    """
    Cost center model for tracking expenses by department/project
    """
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Cost Center Code')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Cost Center Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Cost Center Name (Arabic)')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    manager = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_cost_centers',
        verbose_name=_('Manager')
    )

    budget_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Budget Amount')
    )

    class Meta:
        verbose_name = _('Cost Center')
        verbose_name_plural = _('Cost Centers')
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def total_expenses(self):
        """Calculate total expenses for this cost center"""
        from django.db.models import Sum
        return JournalEntry.objects.filter(
            cost_center=self,
            is_posted=True,
            debit_amount__gt=0
        ).aggregate(total=Sum('debit_amount'))['total'] or 0

    @property
    def budget_utilization(self):
        """Calculate budget utilization percentage"""
        if self.budget_amount > 0:
            return (self.total_expenses / self.budget_amount) * 100
        return 0


class JournalEntry(BaseModel):
    """
    Journal entry model for double-entry bookkeeping
    """
    ENTRY_TYPES = (
        ('manual', _('Manual Entry')),
        ('automatic', _('Automatic Entry')),
        ('adjustment', _('Adjustment Entry')),
        ('closing', _('Closing Entry')),
    )

    reference_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Reference Number')
    )

    entry_date = models.DateField(
        verbose_name=_('Entry Date')
    )

    entry_type = models.CharField(
        max_length=20,
        choices=ENTRY_TYPES,
        default='manual',
        verbose_name=_('Entry Type')
    )

    description = models.TextField(
        verbose_name=_('Description')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='journal_entries',
        verbose_name=_('Account')
    )

    cost_center = models.ForeignKey(
        CostCenter,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entries',
        verbose_name=_('Cost Center')
    )

    debit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Debit Amount')
    )

    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Credit Amount')
    )

    is_posted = models.BooleanField(
        default=False,
        verbose_name=_('Is Posted')
    )

    posted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posted_entries',
        verbose_name=_('Posted By')
    )

    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Posted At')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_entries',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Journal Entry')
        verbose_name_plural = _('Journal Entries')
        ordering = ['-entry_date', '-created_at']

    def __str__(self):
        return f"{self.reference_number} - {self.description[:50]}"

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError(_('An entry cannot have both debit and credit amounts'))
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError(_('An entry must have either a debit or credit amount'))


class FinancialYear(BaseModel):
    """
    Financial year model
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Financial Year Name')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    is_current = models.BooleanField(
        default=False,
        verbose_name=_('Is Current Year')
    )

    is_closed = models.BooleanField(
        default=False,
        verbose_name=_('Is Closed')
    )

    class Meta:
        verbose_name = _('Financial Year')
        verbose_name_plural = _('Financial Years')
        ordering = ['-start_date']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_current:
            # Ensure only one financial year is current
            FinancialYear.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class Bank(BaseModel):
    """
    Bank model for managing bank accounts
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_('Bank Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Bank Name (Arabic)')
    )

    account_number = models.CharField(
        max_length=50,
        verbose_name=_('Account Number')
    )

    account_name = models.CharField(
        max_length=200,
        verbose_name=_('Account Name')
    )

    branch = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Branch')
    )

    swift_code = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('SWIFT Code')
    )

    iban = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('IBAN')
    )

    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Opening Balance')
    )

    current_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Current Balance')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='bank_accounts',
        verbose_name=_('Linked Account')
    )

    class Meta:
        verbose_name = _('Bank Account')
        verbose_name_plural = _('Bank Accounts')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.account_number}"


class Invoice(BaseModel):
    """
    Invoice model for generating student invoices
    """
    INVOICE_TYPES = (
        ('tuition', _('Tuition Invoice')),
        ('fees', _('Fees Invoice')),
        ('other', _('Other Invoice')),
    )

    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('sent', _('Sent')),
        ('paid', _('Paid')),
        ('overdue', _('Overdue')),
        ('cancelled', _('Cancelled')),
    )

    invoice_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Invoice Number')
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='invoices',
        verbose_name=_('Student')
    )

    invoice_type = models.CharField(
        max_length=20,
        choices=INVOICE_TYPES,
        verbose_name=_('Invoice Type')
    )

    invoice_date = models.DateField(
        verbose_name=_('Invoice Date')
    )

    due_date = models.DateField(
        verbose_name=_('Due Date')
    )

    subtotal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Subtotal')
    )

    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Tax Amount')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Discount Amount')
    )

    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Total Amount')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('Status')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_invoices',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Invoice')
        verbose_name_plural = _('Invoices')
        ordering = ['-invoice_date']

    def __str__(self):
        return f"{self.invoice_number} - {self.student.full_name}"

    @property
    def is_overdue(self):
        from datetime import date
        return self.status in ['sent'] and self.due_date < date.today()

    def save(self, *args, **kwargs):
        # Auto-calculate total amount
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        super().save(*args, **kwargs)


class InvoiceItem(BaseModel):
    """
    Invoice line items model
    """
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Invoice')
    )

    description = models.CharField(
        max_length=200,
        verbose_name=_('Description')
    )

    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=1,
        verbose_name=_('Quantity')
    )

    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Unit Price')
    )

    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Total Price')
    )

    fee_type = models.ForeignKey(
        FeeType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Fee Type')
    )

    class Meta:
        verbose_name = _('Invoice Item')
        verbose_name_plural = _('Invoice Items')
        ordering = ['invoice', 'id']

    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.description}"

    def save(self, *args, **kwargs):
        # Auto-calculate total price
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)
