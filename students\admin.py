from django.contrib import admin
from .models import Grade, Class, Parent, Student


@admin.register(Grade)
class GradeAdmin(admin.ModelAdmin):
    """
    Grade admin
    """
    list_display = ('name', 'level', 'is_active')
    list_filter = ('is_active', 'level')
    search_fields = ('name', 'name_ar')
    ordering = ('level',)


@admin.register(Class)
class ClassAdmin(admin.ModelAdmin):
    """
    Class admin
    """
    list_display = ('name', 'grade', 'academic_year', 'class_teacher', 'current_students_count', 'max_students', 'is_active')
    list_filter = ('grade', 'academic_year', 'is_active')
    search_fields = ('name', 'grade__name', 'room_number')
    raw_id_fields = ('class_teacher',)


@admin.register(Parent)
class ParentAdmin(admin.ModelAdmin):
    """
    Parent admin
    """
    list_display = ('father_name', 'mother_name', 'father_phone', 'user', 'is_active')
    list_filter = ('is_active', 'created_at')
    search_fields = ('father_name', 'mother_name', 'father_phone', 'user__username')
    raw_id_fields = ('user',)


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    """
    Student admin
    """
    list_display = ('student_id', 'full_name', 'current_class', 'parent', 'admission_date', 'is_graduated', 'is_active')
    list_filter = ('gender', 'current_class', 'is_graduated', 'is_active', 'admission_date')
    search_fields = ('student_id', 'admission_number', 'first_name', 'last_name', 'first_name_ar', 'last_name_ar')
    raw_id_fields = ('user', 'parent', 'current_class')
    readonly_fields = ('age',)

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'student_id', 'admission_number', 'first_name', 'last_name', 'first_name_ar', 'last_name_ar')
        }),
        ('Personal Details', {
            'fields': ('date_of_birth', 'gender', 'nationality', 'national_id', 'passport_number', 'blood_type', 'photo')
        }),
        ('Academic Information', {
            'fields': ('parent', 'current_class', 'admission_date', 'previous_school')
        }),
        ('Medical Information', {
            'fields': ('medical_conditions', 'allergies', 'special_needs')
        }),
        ('Status', {
            'fields': ('is_graduated', 'graduation_date', 'is_active')
        }),
    )
