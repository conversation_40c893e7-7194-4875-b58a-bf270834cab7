from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from django.conf import settings
from core.models import BaseModel, AcademicYear


class Grade(BaseModel):
    """
    Grade/Level model (e.g., Grade 1, Grade 2, etc.)
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Grade Name')
    )

    name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Grade Name (Arabic)')
    )

    level = models.PositiveIntegerField(
        verbose_name=_('Grade Level')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    class Meta:
        verbose_name = _('Grade')
        verbose_name_plural = _('Grades')
        ordering = ['level']

    def __str__(self):
        return self.name


class Class(BaseModel):
    """
    Class/Section model
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Class Name')
    )

    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='classes',
        verbose_name=_('Grade')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='classes',
        verbose_name=_('Academic Year')
    )

    class_teacher = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_classes',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('Class Teacher')
    )

    max_students = models.PositiveIntegerField(
        default=30,
        verbose_name=_('Maximum Students')
    )

    room_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Room Number')
    )

    class Meta:
        verbose_name = _('Class')
        verbose_name_plural = _('Classes')
        unique_together = ['name', 'grade', 'academic_year']
        ordering = ['grade__level', 'name']

    def __str__(self):
        return f"{self.grade.name} - {self.name}"

    @property
    def current_students_count(self):
        return self.students.filter(is_active=True).count()


class Parent(BaseModel):
    """
    Parent model
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='parent_profile',
        limit_choices_to={'user_type': 'parent'},
        verbose_name=_('User Account')
    )

    father_name = models.CharField(
        max_length=100,
        verbose_name=_('Father Name')
    )

    mother_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Mother Name')
    )

    father_phone = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Father Phone')
    )

    mother_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Mother Phone')
    )

    father_occupation = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Father Occupation')
    )

    mother_occupation = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Mother Occupation')
    )

    father_workplace = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Father Workplace')
    )

    mother_workplace = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Mother Workplace')
    )

    home_address = models.TextField(
        verbose_name=_('Home Address')
    )

    emergency_contact = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Emergency Contact')
    )

    emergency_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Emergency Phone')
    )

    class Meta:
        verbose_name = _('Parent')
        verbose_name_plural = _('Parents')

    def __str__(self):
        return f"{self.father_name} - {self.user.username}"


class Student(BaseModel):
    """
    Student model
    """
    GENDER_CHOICES = (
        ('M', _('Male')),
        ('F', _('Female')),
    )

    BLOOD_TYPE_CHOICES = (
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-'),
    )

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='student_profile',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('User Account')
    )

    student_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Student ID')
    )

    admission_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Admission Number')
    )

    first_name = models.CharField(
        max_length=50,
        verbose_name=_('First Name')
    )

    last_name = models.CharField(
        max_length=50,
        verbose_name=_('Last Name')
    )

    first_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('First Name (Arabic)')
    )

    last_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Last Name (Arabic)')
    )

    date_of_birth = models.DateField(
        verbose_name=_('Date of Birth')
    )

    gender = models.CharField(
        max_length=1,
        choices=GENDER_CHOICES,
        verbose_name=_('Gender')
    )

    nationality = models.CharField(
        max_length=50,
        verbose_name=_('Nationality')
    )

    national_id = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('National ID')
    )

    passport_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Passport Number')
    )

    blood_type = models.CharField(
        max_length=3,
        choices=BLOOD_TYPE_CHOICES,
        blank=True,
        null=True,
        verbose_name=_('Blood Type')
    )

    parent = models.ForeignKey(
        Parent,
        on_delete=models.CASCADE,
        related_name='children',
        verbose_name=_('Parent')
    )

    current_class = models.ForeignKey(
        Class,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='students',
        verbose_name=_('Current Class')
    )

    admission_date = models.DateField(
        verbose_name=_('Admission Date')
    )

    previous_school = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Previous School')
    )

    medical_conditions = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Medical Conditions')
    )

    allergies = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Allergies')
    )

    special_needs = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Special Needs')
    )

    photo = models.ImageField(
        upload_to='students/photos/',
        blank=True,
        null=True,
        verbose_name=_('Photo')
    )

    is_graduated = models.BooleanField(
        default=False,
        verbose_name=_('Is Graduated')
    )

    graduation_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Graduation Date')
    )

    class Meta:
        verbose_name = _('Student')
        verbose_name_plural = _('Students')
        ordering = ['first_name', 'last_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.student_id})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name_ar(self):
        if self.first_name_ar and self.last_name_ar:
            return f"{self.first_name_ar} {self.last_name_ar}"
        return self.full_name

    @property
    def age(self):
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - (
            (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
        )
