from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView
from django.http import HttpResponse

# Finance Dashboard
class FinanceDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/dashboard.html'

# Accounts Management Views
class AccountsTreeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_tree.html'

class CostCenterView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cost_center.html'

class CostCenterReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cost_center_report.html'

class DailyEntriesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_entries.html'

class CreateDailyEntryView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/create_daily_entry.html'

class DailyEntriesHistoryView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_entries_history.html'

class FinancialYearsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/financial_years.html'

class BanksView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/banks.html'

class SafesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/safes.html'

class ReceiptVoucherView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/receipt_voucher.html'

class ExchangePermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/exchange_permission.html'

class TrialBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/trial_balance.html'

class AccountStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/account_statement.html'

class SearchReceiptsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/search_receipts.html'

class MonthlyAccountStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/monthly_statement.html'

class OpeningBalancesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/opening_balances.html'

class CashBankStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cash_bank_statement.html'

class BalanceSheetView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/balance_sheet.html'

class IncomeStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/income_statement.html'

# Student Accounts Views
class FeesItemsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fees_items.html'

class GroupedOptionalFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_fees.html'

class GradeFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grade_fees.html'

class FeesPaymentView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fees_payment.html'

class PaymentHistoryView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_history.html'

class AccountsTreeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_tree.html'

class JournalEntriesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/journal_entries.html'

class TaxCodeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/tax_code.html'

class DailyPaymentsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_payments_report.html'

class StudentDebitsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/student_debits.html'

class AccountsAggregateReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_aggregate_report.html'

class PaymentsByStagesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payments_by_stages.html'

class PaymentAccountReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_account_report.html'

class ItemsSavesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/items_saves_report.html'

class TaxPaymentReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/tax_payment_report.html'

# Payment Permissions Views
class PaymentPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_permission.html'

class PayPaymentPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/pay_permission.html'

class PermissionReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/permission_reports.html'

# Registration and Discounts Views
class RegistrationFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/registration_fees.html'

class DiscountSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_settings.html'

class DiscountSettingsDetailsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_details.html'

class AddDiscountView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/add_discount.html'

class RemoveStudentDebitsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/remove_debits.html'

class DiscountReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_report.html'

class BusPaymentsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/bus_payments_report.html'

class FeesRequestsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fees_requests.html'

class RecoveryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/recovery_report.html'

class PaymentStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_statement.html'

class TotalPaymentStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/total_payment_statement.html'

class UnpaidFeesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/unpaid_fees_report.html'

class AddStudentOpeningBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/opening_balance_student.html'

class PaymentRequestView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_request.html'

class GroupedDiscountView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_discount.html'

class GroupedPaymentTransactionsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_payments.html'

# Reports Views
class FinancialReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports.html'

class EInvoiceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/einvoice_report.html'

class ZakatIncomeReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/zakat_income_report.html'
