from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView
from django.http import HttpResponse

def placeholder_view(request):
    return HttpResponse("HR module - Coming soon!")

# HR Dashboard
class HRDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/dashboard.html'

# Employee Management
class EmployeeListView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employees.html'

class EmployeeCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_form.html'

class EmployeeDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_detail.html'

class EmployeeUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_form.html'

class EmployeeDeleteView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_confirm_delete.html'

# Department Management
class DepartmentListView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/departments.html'

class DepartmentCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/department_form.html'

class DepartmentUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/department_form.html'

# Position Management
class PositionListView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/positions.html'

class PositionCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/position_form.html'

class PositionUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/position_form.html'

# General Settings
class RegistrationFilesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/registration_files.html'

class EmployeeAffairsSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/affairs_settings.html'

class WorkSystemView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/work_system.html'

class AttendanceDevicesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_devices.html'

class AttendanceDevices2View(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_devices_2.html'

class PermissionTypesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/permission_types.html'

class HolidayTypesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/holiday_types.html'

class AllowanceTypesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/allowance_types.html'

class DeductionTypesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/deduction_types.html'

class PublicHolidaysView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/public_holidays.html'

# Work System Settings
class DeductionRulesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/deduction_rules.html'

class ExtraTimeRulesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/extra_time_rules.html'

class RewardsRulesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/rewards_rules.html'

class PenaltiesRulesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/penalties_rules.html'

# Attendance and Presence
class AttendanceView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance.html'

class ManualAttendanceView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/manual_attendance.html'

class AttendanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_reports.html'

class AttendanceSummaryView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_summary.html'

class AttendanceDevicesDataView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_devices_data.html'

# Permissions and Leaves
class PermissionsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/permissions.html'

class AddPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/add_permission.html'

class ApprovePermissionsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/approve_permissions.html'

class PermissionReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/permission_reports.html'

# Holidays and Vacations
class HolidaysView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/holidays.html'

class VacationRequestsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/vacation_requests.html'

class VacationBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/vacation_balance.html'

# Payroll
class PayrollView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/payroll.html'

class GeneratePayrollView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/generate_payroll.html'

class PayrollReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/payroll_reports.html'

class SalaryStructureView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/salary_structure.html'

class AllowancesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/allowances.html'

class DeductionsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/deductions.html'

class BonusesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/bonuses.html'

# Performance and Evaluation
class PerformanceView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/performance.html'

# HR Reports
class HRReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/reports.html'

class EmployeeReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_reports.html'

class EmployeeAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_attendance_report.html'

class LeaveReportView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/leave_report.html'

class EmployeePerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_performance_report.html'

class PerformanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/performance_reports.html'
