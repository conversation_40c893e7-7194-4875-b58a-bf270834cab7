from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from core.models import BaseModel


class Department(BaseModel):
    """
    Department model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Department Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Department Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Department Code')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    head = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments',
        verbose_name=_('Department Head')
    )

    class Meta:
        verbose_name = _('Department')
        verbose_name_plural = _('Departments')
        ordering = ['name']

    def __str__(self):
        return self.name


class Position(BaseModel):
    """
    Job position model
    """
    title = models.CharField(
        max_length=100,
        verbose_name=_('Position Title')
    )

    title_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Position Title (Arabic)')
    )

    department = models.ForeignKey(
        Department,
        on_delete=models.CASCADE,
        related_name='positions',
        verbose_name=_('Department')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Job Description')
    )

    requirements = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Requirements')
    )

    min_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Minimum Salary')
    )

    max_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Maximum Salary')
    )

    class Meta:
        verbose_name = _('Position')
        verbose_name_plural = _('Positions')
        ordering = ['department', 'title']

    def __str__(self):
        return f"{self.title} - {self.department.name}"


class Employee(BaseModel):
    """
    Employee model
    """
    EMPLOYMENT_STATUS = (
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('terminated', _('Terminated')),
        ('resigned', _('Resigned')),
        ('retired', _('Retired')),
    )

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='employee_profile',
        verbose_name=_('User Account')
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Employee ID')
    )

    position = models.ForeignKey(
        Position,
        on_delete=models.CASCADE,
        related_name='employees',
        verbose_name=_('Position')
    )

    hire_date = models.DateField(
        verbose_name=_('Hire Date')
    )

    termination_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Termination Date')
    )

    employment_status = models.CharField(
        max_length=20,
        choices=EMPLOYMENT_STATUS,
        default='active',
        verbose_name=_('Employment Status')
    )

    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Salary')
    )

    emergency_contact_name = models.CharField(
        max_length=100,
        verbose_name=_('Emergency Contact Name')
    )

    emergency_contact_phone = models.CharField(
        max_length=20,
        verbose_name=_('Emergency Contact Phone')
    )

    emergency_contact_relationship = models.CharField(
        max_length=50,
        verbose_name=_('Emergency Contact Relationship')
    )

    class Meta:
        verbose_name = _('Employee')
        verbose_name_plural = _('Employees')
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} ({self.employee_id})"
